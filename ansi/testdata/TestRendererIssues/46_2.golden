
  [38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m
   [38;5;252mDependency[0m                          │ [38;5;252mInstall…[0m │ [38;5;252mOperation[0m               [38;5;252m [0m[38;5;252m [0m
  ─────────────────────────────────────┼──────────┼─────────────────────────[38;5;252m [0m[38;5;252m [0m
   [38;5;252mxdg-open (Linux), open(1) (macOS),[m  │ [38;5;252mbase[0m     │ [38;5;252mdesktop[0m[38;5;252m opener[0m          [38;5;252m [0m[38;5;252m [0m
   [38;5;252mcygstart [0m[38;5;252m(Cygwin)[0m                   │          │                         [38;5;252m [0m[38;5;252m [0m
   [38;5;252mfile, coreutils (cp, mv, rm),[0m[38;5;252m xargs[0m │ [38;5;252mbase[0m     │ [38;5;252mfile type, copy, move[m   [38;5;252m [0m[38;5;252m [0m[0m
[0m  [38;5;252m[m                                     │          │ [38;5;252mand[0m[38;5;252m remove[0m              [38;5;252m [0m[38;5;252m [0m
   [38;5;252mtar, (un)zip [[0m[38;5;252matool/bsdtar for more[m │ [38;5;252mbase[0m     │ [38;5;252mcreate, list, extract[m   [38;5;252m [0m[38;5;252m [0m[0m
[0m  [38;5;252m[m [38;5;252mformats[0m[38;5;252m][0m                            │          │ [38;5;252mtar, gzip, bzip2,[0m[38;5;252m zip[0m   [38;5;252m [0m[38;5;252m [0m
   [38;5;252marchivemount, fusermount[0m[38;5;252m(3)[0m         │ [38;5;252moptional[0m │ [38;5;252mmount, unmount[0m[38;5;252m archives[0m [38;5;252m [0m[38;5;252m [0m
   [38;5;252msshfs, [0m[38;5;252m[38;5;35;1mrclone[1][0m[0m[38;5;252m, fusermount[0m[38;5;252m(3)[0m     │ [38;5;252moptional[0m │ [38;5;252mmount, unmount[0m[38;5;252m remotes[0m  [38;5;252m [0m[38;5;252m [0m
   [38;5;252mtrash-cli[0m                           │ [38;5;252moptional[0m │ [38;5;252mtrash files (default[m    [38;5;252m [0m[38;5;252m [0m[0m
[0m  [38;5;252m[m                                     │          │ [38;5;252maction:[0m[38;5;252m rm)[0m             [38;5;252m [0m[38;5;252m [0m
   [38;5;252mvlock (Linux), bashlock (macOS),[m    │ [38;5;252moptional[0m │ [38;5;252mterminal locker[m         [38;5;252m [0m[38;5;252m [0m[0m
[0m  [38;5;252m[m [38;5;252mlock(1) [0m[38;5;252m(BSD)[0m                       │          │ [38;5;252m(fallback: [0m[38;5;252m[38;5;35;1mcmatrix[2][0m[0m[38;5;252m)[0m  [38;5;252m [0m[38;5;252m [0m
   [38;5;252madvcpmv (Linux) ([0m[38;5;252m[38;5;35;1mintegration[3][0m[0m[38;5;252m)[0m    │ [38;5;252moptional[0m │ [38;5;252mcopy, move[0m[38;5;252m progress[0m     [38;5;252m [0m[38;5;252m [0m
   [38;5;252m[38;5;203;48;5;236m $VISUAL [0m[0m[38;5;252m (else [0m[38;5;252m[38;5;203;48;5;236m $EDITOR [0m[0m[38;5;252m),[m         │ [38;5;252moptional[0m │ [38;5;252mfallback vi, less,[0m[38;5;252m sh[0m   [38;5;252m [0m[38;5;252m [0m
   [38;5;252m[0m[38;5;252m[38;5;203;48;5;236m $PAGER [0m[0m[38;5;252m, [0m[38;5;252m[38;5;203;48;5;236m $SHELL [0m[0m                  │          │                         [38;5;252m[38;5;252m [0m[38;5;252m [0m[0m
[0m[38;5;252m[0m  [38;5;252m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[0m
[0m[38;5;35;1m[0m  [38;5;35;1m[1]: rclone[0m[38;5;252m [0m[38;5;30;4mhttps://rclone.org/[0m[38;5;252m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[0m
[0m[38;5;35;1m[0m  [38;5;35;1m[2]: cmatrix[0m[38;5;252m [0m[38;5;30;4mhttps://github.com/abishekvashok/cmatrix[0m[38;5;252m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[38;5;252m [0m[0m
[0m[38;5;35;1m[0m  [38;5;35;1m[3]: integration[0m[38;5;252m [0m[38;5;30;4mhttps://github.com/jarun/nnn/wiki/Advanced-use-cases#show-…[0m


| Library                                                     | Version    |
| ----------------------------------------------------------- | ---------- |
| [ESMF](https://github.com/esmf-org/esmf)                    | v8.6.1     |
| [FMS](https://github.com/NOAA-GFDL/FMS/)                    | 2024.01.02 |
| [netCDF](https://github.com/Unidata/netcdf-c)               | 4.9.2      |
| [netCDF Fortran](https://github.com/Unidata/netcdf-fortran) | 4.6.1      |
| [netCDF C++](https://github.com/Unidata/netcdf-cxx4)        | 4.3.1      |
| [HDF5](https://portal.hdfgroup.org/display/support)         | 1.10.11    |
| [HDF4](https://portal.hdfgroup.org/display/support)         | 4.2.16-2   |
| [GFE](https://github.com/Goddard-<PERSON>-Ecosystem/GFE)     | v1.16.0    |
| [xgboost](https://github.com/dmlc/xgboost)                  | v1.6.0     |
| [libyaml](https://github.com/yaml/libyaml.git)              | 0.2.5      |
| [antlr2](https://www.antlr2.org/)                           | 2.7.7      |
| [GSL](https://www.gnu.org/software/gsl/)                    | 2.7        |
| [jpeg](http://www.ijg.org/)                                 | 9e         |
| [zlib](http://www.zlib.net/)                                | 1.3.1      |
| [szip](https://support.hdfgroup.org/doc_resource/SZIP/)     | 2.1.1      |
| [cURL](https://curl.haxx.se/)                               | 8.8.0      |
| [UDUNITS2](https://github.com/GMAO-SI-Team/UDUNITS-2.git)   | 2.2.28     |
| [NCO](http://nco.sourceforge.net/)                          | 5.2.6      |
| [CDO](https://code.mpimet.mpg.de/projects/cdo)              | 2.3.0      |
| [nccmp](https://gitlab.com/remikz/nccmp)                    | *******    |
| [HDF-EOS2](https://wiki.earthdata.nasa.gov/display/DAS)     | 3.0        |
| [HDF-EOS5](https://wiki.earthdata.nasa.gov/display/DAS)     | 2.0        |
| [SDP Toolkit](https://wiki.earthdata.nasa.gov/display/DAS)  | 5.2.20     |

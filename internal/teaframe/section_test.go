package teaframe

import (
	"strings"
	"testing"

	"github.com/charmbracelet/lipgloss"
)

func TestCustomStyleRender(t *testing.T) {
	// Create a basic style
	style := NewCustomStyle()

	// Test basic rendering
	rendered := style.Render("Hello")
	if rendered != "Hello" {
		t.<PERSON>("Expected 'Hello', got '%s'", rendered)
	}

	// Test with transformation
	transformStyle := NewCustomStyle().
		WithProcessContent(true).
		WithTransform(strings.ToUpper)

	rendered = transformStyle.Render("Hello")
	if rendered != "HELLO" {
		t.<PERSON><PERSON><PERSON>("Expected 'HELLO', got '%s'", rendered)
	}
}

func TestCustomStyleWithTitle(t *testing.T) {
	// Create a style with border
	style := NewCustomStyle().
		ApplyStyle(lipgloss.NewStyle().
			BorderStyle(lipgloss.RoundedBorder()).
			Width(20))

	// Test rendering with title
	rendered := style.RenderWithTitle("TITLE", "OverflowContent")

	// Check that the title is in the output
	if !strings.Contains(rendered, "TITLE") {
		t.<PERSON><PERSON>("Expected title 'TITLE' in output, got: %s", rendered)
	}

	// Check that the content is in the output
	if !strings.Contains(rendered, "OverflowContent") {
		t.Errorf("Expected content 'OverflowContent' in output, got: %s", rendered)
	}
}

func TestCustomStyleWithFooter(t *testing.T) {
	// Create a style with border
	style := NewCustomStyle().
		ApplyStyle(lipgloss.NewStyle().
			BorderStyle(lipgloss.RoundedBorder()).
			Width(20))

	// Test rendering with footer
	rendered := style.RenderWithFooter("OverflowContent", "FOOTER")

	// Check that the footer is in the output
	if !strings.Contains(rendered, "FOOTER") {
		t.Errorf("Expected footer 'FOOTER' in output, got: %s", rendered)
	}

	// Check that the content is in the output
	if !strings.Contains(rendered, "OverflowContent") {
		t.Errorf("Expected content 'OverflowContent' in output, got: %s", rendered)
	}
}

func TestCustomStyleWrapping(t *testing.T) {
	// Create a style with width
	style := NewCustomStyle().
		ApplyStyle(lipgloss.NewStyle().
			Width(10))

	// Test rendering with wrapping
	longText := "This is a long text that should wrap"
	rendered := style.RenderWrapped(longText)

	// The rendered text should have multiple lines due to wrapping
	lines := strings.Split(rendered, "\n")
	if len(lines) <= 1 {
		t.Errorf("Expected multiple lines due to wrapping, got: %s", rendered)
	}
}

func TestCustomStyleWithTitleAndFooter(t *testing.T) {
	// Create a style with border
	style := NewCustomStyle().
		ApplyStyle(lipgloss.NewStyle().
			BorderStyle(lipgloss.RoundedBorder()).
			Width(20))

	// Test rendering with title and footer
	rendered := style.RenderWithTitleAndFooter("HEADER", "OverflowContent", "FOOTER")

	// Check that the title is in the output
	if !strings.Contains(rendered, "HEADER") {
		t.Errorf("Expected title 'HEADER' in output, got: %s", rendered)
	}

	// Check that the footer is in the output
	if !strings.Contains(rendered, "FOOTER") {
		t.Errorf("Expected footer 'FOOTER' in output, got: %s", rendered)
	}

	// Check that the content is in the output
	if !strings.Contains(rendered, "OverflowContent") {
		t.Errorf("Expected content 'OverflowContent' in output, got: %s", rendered)
	}

	// Check the width of each line
	lines := strings.Split(rendered, "\n")
	expectedWidth := 22 // 20 (width) + 2 (border)
	for i, line := range lines {
		lineWidth := lipgloss.Width(line)
		if lineWidth != expectedWidth {
			t.Errorf("Line %d has incorrect width: expected %d, got %d\nLine: '%s'",
				i, expectedWidth, lineWidth, line)
		}
	}
}

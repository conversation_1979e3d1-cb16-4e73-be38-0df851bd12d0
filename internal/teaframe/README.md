# TeaFrame Component

A reusable terminal UI component for displaying text in a styled frame using the Bubble Tea framework.

## Features

- Customizable border color
- Adjustable padding
- Multiple border styles
- Text alignment options
- Configurable quit keys
- Auto-quit mode (exits immediately after rendering)
- Empty line after the frame for better readability

## Usage

### Basic Usage

```go
package main

import (
    "fmt"
    "os"

    "github.com/oxio/aia/internal/teaframe"
)

func main() {
    // Create a new frame with default settings (violet rounded border)
    frame := teaframe.NewFrame("Hello! This is a simple tea program with a violet frame.\n\nPress q, ESC, or Ctrl+C to quit.")

    // Run the program
    if err := teaframe.Run(frame); err != nil {
        fmt.Printf("Error running program: %v\n", err)
        os.Exit(1)
    }
}
```

### Custom Styling

```go
package main

import (
    "fmt"
    "os"

    "github.com/charmbracelet/lipgloss"
    "github.com/oxio/aia/internal/teaframe"
)

func main() {
    // Create a frame with custom settings
    frame := teaframe.NewFrame("This is a custom frame example with different styling.\n\nPress q, ESC, or Ctrl+C to quit.")

    // Customize the frame
    frame = frame.
        WithBorderColor("#FF69B4").                // Hot pink color
        WithPadding(2, 3).                         // More padding
        WithBorderStyle(lipgloss.DoubleBorder()).  // Double border style
        WithAlignment(lipgloss.Left)               // Left-aligned text

    // Run the program
    if err := teaframe.Run(frame); err != nil {
        fmt.Printf("Error running program: %v\n", err)
        os.Exit(1)
    }
}
```

## Customization Options

### Border Colors

You can use any valid color string:

```go
frame = frame.WithBorderColor("#8A2BE2")  // Violet
frame = frame.WithBorderColor("#FF69B4")  // Hot pink
frame = frame.WithBorderColor("#00FF00")  // Green
```

### Border Styles

Available border styles from lipgloss:

```go
frame = frame.WithBorderStyle(lipgloss.RoundedBorder())  // Default
frame = frame.WithBorderStyle(lipgloss.DoubleBorder())
frame = frame.WithBorderStyle(lipgloss.NormalBorder())
frame = frame.WithBorderStyle(lipgloss.ThickBorder())
frame = frame.WithBorderStyle(lipgloss.HiddenBorder())
```

### Text Alignment

```go
frame = frame.WithAlignment(lipgloss.Center)  // Default
frame = frame.WithAlignment(lipgloss.Left)
frame = frame.WithAlignment(lipgloss.Right)
```

### Padding

```go
frame = frame.WithPadding(1, 2)  // Default (vertical, horizontal)
frame = frame.WithPadding(2, 3)  // More padding
frame = frame.WithPadding(0, 0)  // No padding
```

### Quit Keys

```go
frame = frame.WithQuitKeys([]string{"q", "ctrl+c", "esc"})  // Default
frame = frame.WithQuitKeys([]string{"q"})                   // Only 'q' quits
```

### Auto-Quit Mode

By default, frames will automatically quit after rendering. You can disable this behavior:

```go
frame = frame.WithAutoQuit(true)   // Default - exits immediately after rendering
frame = frame.WithAutoQuit(false)  // Interactive mode - waits for user input
```

package teaframe

import (
	"fmt"
	"strings"

	"github.com/charmbracelet/lipgloss"
	"github.com/muesli/reflow/wrap"
)

// CustomStyle embeds lipgloss.Style and overrides its Render method
type CustomStyle struct {
	lipgloss.Style
	// Add any additional fields here that you want to include
	processContent bool                // Whether to process content before rendering
	transformFunc  func(string) string // Optional transformation function
}

// NewCustomStyle creates a new CustomStyle
func NewCustomStyle() CustomStyle {
	return CustomStyle{
		Style:          lipgloss.NewStyle(),
		processContent: true,
		transformFunc:  nil,
	}
}

// FromStyle creates a CustomStyle from an existing lipgloss.Style
func FromStyle(style lipgloss.Style) CustomStyle {
	return CustomStyle{
		Style:          style,
		processContent: true,
		transformFunc:  nil,
	}
}

// WithProcessContent sets whether to process content before rendering
func (s CustomStyle) WithProcessContent(process bool) CustomStyle {
	s.processContent = process
	return s
}

// WithTransform sets a transformation function to apply to content before rendering
func (s CustomStyle) WithTransform(fn func(string) string) CustomStyle {
	s.transformFunc = fn
	return s
}

// Render overrides the lipgloss.Style Render method to add custom functionality
func (s CustomStyle) Render(strs ...string) string {
	// Join all strings with newlines
	content := strings.Join(strs, "\n")

	// Apply pre-processing if enabled
	if s.processContent {
		// Remove any ANSI escape sequences if needed
		// content = removeANSI(content)

		// Apply custom transformation if provided
		if s.transformFunc != nil {
			content = s.transformFunc(content)
		}
	}

	// Call the original Render method from the embedded style
	rendered := s.Style.Render(content)

	// Add custom post-processing here if needed
	// For example, you could add additional formatting, logging, etc.

	return rendered
}

// Copy creates a copy of the CustomStyle
func (s CustomStyle) Copy() CustomStyle {
	return CustomStyle{
		Style:          s.Style.Copy(),
		processContent: s.processContent,
		transformFunc:  s.transformFunc,
	}
}

// String implements the Stringer interface
func (s CustomStyle) String() string {
	return s.Render()
}

// RenderWrapped renders the content with automatic wrapping based on the style's width
func (s CustomStyle) RenderWrapped(strs ...string) string {
	content := strings.Join(strs, "\n")

	// Get the available width for content
	width := s.GetWidth()
	if width <= 0 {
		// If no width is set, just use the regular Render method
		return s.Render(content)
	}

	// Account for horizontal padding and border
	contentWidth := width - s.GetHorizontalFrameSize()
	if contentWidth <= 0 {
		contentWidth = width
	}

	// Wrap the content to the available width
	wrapped := wrap.String(content, contentWidth)

	// Render with the wrapped content
	return s.Render(wrapped)
}

// RenderWithTitle renders content with a title embedded in the top border
func (s CustomStyle) RenderWithTitle(title string, content string) string {
	// Get border style
	border := s.GetBorderStyle()
	if border.Top == "" {
		// If no border is set, use a default border
		border = lipgloss.RoundedBorder()
	}

	// Create a copy of the style for rendering
	tempStyle := s.Copy()

	// Render the content first to get the dimensions
	renderedContent := tempStyle.Render(content)
	lines := strings.Split(renderedContent, "\n")

	// If there are no lines, return empty string
	if len(lines) == 0 {
		return ""
	}

	// Get the width of the first line
	firstLineWidth := lipgloss.Width(lines[0])

	// Create the title part
	titlePrefix := border.TopLeft + border.Top // "╭─"
	titleSuffixChar := border.TopRight

	// Calculate how many border characters we need
	borderCharsNeeded := firstLineWidth - lipgloss.Width(titlePrefix) - lipgloss.Width(title) - 2 - lipgloss.Width(titleSuffixChar)
	if borderCharsNeeded < 0 {
		borderCharsNeeded = 0
	}

	// Create a style for the border characters with the same color as the original border
	borderStyle := lipgloss.NewStyle().Foreground(s.GetBorderColor())

	// style the border parts
	styledPrefix := borderStyle.Render(titlePrefix)
	styledBorder := borderStyle.Render(strings.Repeat(border.Top, borderCharsNeeded))
	styledSuffix := borderStyle.Render(titleSuffixChar)

	// Create the title line with the exact width and styled border
	styledTitle := fmt.Sprintf("%s %s %s%s",
		styledPrefix,
		title,
		styledBorder,
		styledSuffix)

	// Replace the first line (top border) with our title line
	lines[0] = styledTitle

	// Join the lines back together
	return strings.Join(lines, "\n")
}

// RenderWithFooter renders content with a footer message in the bottom border
func (s CustomStyle) RenderWithFooter(content string, footer string) string {
	// Get border style
	border := s.GetBorderStyle()
	if border.Top == "" {
		// If no border is set, use a default border
		border = lipgloss.RoundedBorder()
	}

	// Create a copy of the style for rendering
	tempStyle := s.Copy()

	// Render the content first to get the dimensions
	renderedContent := tempStyle.Render(content)
	lines := strings.Split(renderedContent, "\n")

	// If there are no lines, return empty string
	if len(lines) == 0 {
		return ""
	}

	// Get the last line (which should be the bottom border)
	lastLineIdx := len(lines) - 1
	lastLineWidth := lipgloss.Width(lines[lastLineIdx])

	// Create the footer part
	footerPrefix := border.BottomLeft + border.Bottom // "╰─"
	footerSuffixChar := border.BottomRight

	// Calculate how many border characters we need
	borderCharsNeeded := lastLineWidth - lipgloss.Width(footerPrefix) - lipgloss.Width(footer) - 2 - lipgloss.Width(footerSuffixChar)
	if borderCharsNeeded < 0 {
		borderCharsNeeded = 0
	}

	// Create a style for the border characters with the same color as the original border
	borderStyle := lipgloss.NewStyle().Foreground(s.GetBorderColor())

	// style the border parts
	styledPrefix := borderStyle.Render(footerPrefix)
	styledBorder := borderStyle.Render(strings.Repeat(border.Bottom, borderCharsNeeded))
	styledSuffix := borderStyle.Render(footerSuffixChar)

	// Create the footer line with the exact width and styled border
	styledFooter := fmt.Sprintf("%s %s %s%s",
		styledPrefix,
		footer,
		styledBorder,
		styledSuffix)

	// Replace the last line (bottom border) with our footer line
	lines[lastLineIdx] = styledFooter

	// Join the lines back together
	return strings.Join(lines, "\n")
}

// RenderWithTitleAndFooter combines RenderWithTitle and RenderWithFooter
func (s CustomStyle) RenderWithTitleAndFooter(title string, content string, footer string) string {
	// Get border style
	border := s.GetBorderStyle()
	if border.Top == "" {
		// If no border is set, use a default border
		border = lipgloss.RoundedBorder()
	}

	// Create a copy of the style for rendering
	tempStyle := s.Copy()

	// First render the content to get dimensions
	renderedContent := tempStyle.Render(content)
	lines := strings.Split(renderedContent, "\n")

	// If there are no lines, return empty string
	if len(lines) == 0 {
		return ""
	}

	// Get the width of the first line (which should be the top border)
	firstLineWidth := lipgloss.Width(lines[0])
	lastLineIdx := len(lines) - 1

	// Calculate the exact width we need for the title and footer lines
	// This ensures they match the width of the content lines
	targetWidth := firstLineWidth

	// Create the title part
	titlePrefix := border.TopLeft + border.Top // "╭─"

	// Calculate space needed for the title text and spacing
	titleTextWidth := lipgloss.Width(title) + 2 // +2 for the spaces around the title

	// Calculate the remaining width for the border characters
	remainingWidth := targetWidth - lipgloss.Width(titlePrefix) - titleTextWidth

	// Ensure we don't have negative width
	if remainingWidth < 0 {
		remainingWidth = 0
	}

	// Create the title suffix with the exact number of border characters needed
	titleSuffix := strings.Repeat(border.Top, remainingWidth) + border.TopRight

	// style the title
	styledTitle := fmt.Sprintf("%s %s %s", titlePrefix, title, titleSuffix)

	// Create the footer part
	footerPrefix := border.BottomLeft + border.Bottom // "╰─"

	// Calculate space needed for the footer text and spacing
	footerTextWidth := lipgloss.Width(footer) + 2 // +2 for the spaces around the footer

	// Calculate the remaining width for the border characters
	remainingWidth = targetWidth - lipgloss.Width(footerPrefix) - footerTextWidth

	// Ensure we don't have negative width
	if remainingWidth < 0 {
		remainingWidth = 0
	}

	// Create the footer suffix with the exact number of border characters needed
	footerSuffix := strings.Repeat(border.Bottom, remainingWidth) + border.BottomRight

	// style the footer
	styledFooter := fmt.Sprintf("%s %s %s", footerPrefix, footer, footerSuffix)

	// We'll use a different approach - let's recalculate everything from scratch
	// to ensure the exact width

	// For the title line
	// We already have titlePrefix defined above
	// titlePrefix = border.TopLeft + border.Top // "╭─"
	titleSuffixChar := border.TopRight

	// Calculate how many border characters we need
	borderCharsNeeded := targetWidth - lipgloss.Width(titlePrefix) - lipgloss.Width(title) - 2 - lipgloss.Width(titleSuffixChar)
	if borderCharsNeeded < 0 {
		borderCharsNeeded = 0
	}

	// Create a style for the border characters with the same color as the original border
	borderStyle := lipgloss.NewStyle().Foreground(s.GetBorderColor())

	// style the border parts
	styledPrefix := borderStyle.Render(titlePrefix)
	styledBorder := borderStyle.Render(strings.Repeat(border.Top, borderCharsNeeded))
	styledSuffix := borderStyle.Render(titleSuffixChar)

	// Create the title line with the exact width and styled border
	styledTitle = fmt.Sprintf("%s %s %s%s",
		styledPrefix,
		title,
		styledBorder,
		styledSuffix)

	// For the footer line
	// We already have footerPrefix defined above
	// footerPrefix = border.BottomLeft + border.Bottom // "╰─"
	footerSuffixChar := border.BottomRight

	// Calculate how many border characters we need
	borderCharsNeeded = targetWidth - lipgloss.Width(footerPrefix) - lipgloss.Width(footer) - 2 - lipgloss.Width(footerSuffixChar)
	if borderCharsNeeded < 0 {
		borderCharsNeeded = 0
	}

	// style the footer border parts (using the same borderStyle from above)
	styledFooterPrefix := borderStyle.Render(footerPrefix)
	styledFooterBorder := borderStyle.Render(strings.Repeat(border.Bottom, borderCharsNeeded))
	styledFooterSuffix := borderStyle.Render(footerSuffixChar)

	// Create the footer line with the exact width and styled border
	styledFooter = fmt.Sprintf("%s %s %s%s",
		styledFooterPrefix,
		footer,
		styledFooterBorder,
		styledFooterSuffix)

	// Replace the first and last lines
	lines[0] = styledTitle
	lines[lastLineIdx] = styledFooter

	// Join the lines back together
	return strings.Join(lines, "\n")
}

// StyleMethods returns all the methods from the embedded lipgloss.Style
// This allows chaining methods from the original Style
func (s CustomStyle) StyleMethods() lipgloss.Style {
	return s.Style
}

// GetBorderColor extracts the border color from the style
// This is a helper method since lipgloss.Style doesn't expose a direct getter
func (s CustomStyle) GetBorderColor() lipgloss.Color {
	// Default color if we can't determine it
	defaultColor := lipgloss.Color("63") // Purple

	// Create a test string with a border
	testStyle := s.Style.Copy().BorderStyle(lipgloss.RoundedBorder())
	renderedBorder := testStyle.Render("test")

	// If the rendered border contains color codes, we can assume the border has a color
	// This is a simple heuristic and might not work in all cases
	if strings.Contains(renderedBorder, "38;5;") {
		return defaultColor
	}

	return defaultColor
}

// ApplyStyle applies a lipgloss.Style to this CustomStyle
func (s CustomStyle) ApplyStyle(style lipgloss.Style) CustomStyle {
	// Create a new style with the same settings
	newStyle := s.Copy()

	// Apply the provided style to our embedded style
	newStyle.Style = style

	return newStyle
}

// Example usage of CustomStyle:
//
// style := teaframe.NewCustomStyle().
//     WithProcessContent(true).
//     WithTransform(strings.ToUpper)
//
// style = style.ApplyStyle(lipgloss.NewStyle().
//     border(lipgloss.RoundedBorder()).
//     BorderForeground(lipgloss.Color("63")).
//     Padding(1, 2))
//
// fmt.Println(style.RenderWithTitle("TITLE", "Hello, World!"))

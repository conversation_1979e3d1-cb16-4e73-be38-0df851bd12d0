package teaframe

import (
	"github.com/oxio/aia/internal/ui"
	"strings"

	"github.com/charmbracelet/lipgloss"
	"github.com/oxio/aia/internal/colors"
)

// UserChatMessage displays a user message in a chat-like interface
// with a blue border and left alignment
func UserChatMessage(content string) {
	style := ui.NewFrameConfig().
		WithBorderColor(colors.DeepSkyBlue).
		WithAlign(lipgloss.Left).
		WithPadding(1, 2).
		WithIcon("👤").
		WithTitle("You")

	ui.StreamMessage(strings.NewReader(content), style)
}

// AiaChatMessage displays an AI assistant message in a chat-like interface
// with a vibrant pink border and right alignment
func AiaChatMessage(content string) {
	style := ui.NewFrameConfig().
		WithBorderColor(colors.VibrantPink).
		WithAlign(lipgloss.Right).
		WithPadding(1, 2).
		WithIcon("👄").
		WithTitle("AIA")

	ui.StreamMessage(strings.NewReader(content), style)
}

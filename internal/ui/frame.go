package ui

import (
	"bufio"
	"github.com/charmbracelet/bubbles/spinner"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/glamour"
	"github.com/charmbracelet/lipgloss"
	"github.com/oxio/aia/internal/paging"
	"github.com/oxio/aia/internal/terminal"
	"io"
	"strings"
	"sync"
)

// FrameModel represents a styled frame component
type FrameModel struct {
	Content          string
	lastValidContent string
	Width            int
	Height           int
	config           FrameConfig
	QuitKeys         []string // TODO: add support for this
	quitting         bool
	spinnerManager   *SpinnerManagerModel
	LoadingSpinner   *SpinnerWrapper
	ThinkingSpinner  *SpinnerWrapper
	isThinking       bool
	isConnecting     bool
	toBeContinued    bool
	isContinuing     bool
	frameWidth       int
	frameWidthLock   bool
	chunkedWriter    *paging.ChunkedLineWriter // TODO: not needed here
	chunkedReader    *paging.ChunkedLineReader
}

type contentMsg struct {
	tea.Msg
}

// NewFrame creates a new frame with default settings
func NewFrame(content string, chunkedWriter *paging.ChunkedLineWriter, config FrameConfig) *FrameModel {
	// Get the terminal dimensions
	termWidth := terminal.GetWidth()
	termHeight := terminal.GetHeight()
	defaultWidth := int(float64(termWidth) * 0.8)

	return &FrameModel{
		Content:         content,
		Width:           defaultWidth,
		Height:          termHeight,
		config:          config,
		QuitKeys:        []string{"q", "ctrl+c", "esc"},
		spinnerManager:  NewSpinnerManagerModel(),
		LoadingSpinner:  NewLoadingSpinnerWrapper(config).SetTitle("Connecting"),
		ThinkingSpinner: NewThinkingSpinnerWrapper(config),
		isConnecting:    true,
		chunkedWriter:   chunkedWriter,
		chunkedReader:   paging.NewChunkedReader(chunkedWriter),
	}
}

// Init initializes the component
func (m *FrameModel) Init() tea.Cmd {
	mgr, cmd := m.spinnerManager.Update(m.LoadingSpinner)
	m.spinnerManager = mgr.(*SpinnerManagerModel)
	return cmd
}

// Update handles messages and updates the model
func (m *FrameModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {

	case tea.KeyMsg:
		// Check if the pressed key is in the quit keys list
		for _, key := range m.QuitKeys {
			if msg.String() == key {
				m.quitting = true
				return m, tea.Quit
			}
		}

	case tea.WindowSizeMsg:
		// TODO: handle this properly. Can it even be done? middle rendering it should be const because of pagination.
		m.Width = msg.Width
		m.Height = msg.Height

	case contentMsg:
		m.isConnecting = false
		m.Content = m.chunkedReader.CurrentChunk()

	case FinishMsg:
		return m, tea.Quit

	case spinner.TickMsg:
		mgr, cmd := m.spinnerManager.Update(msg)
		m.spinnerManager = mgr.(*SpinnerManagerModel)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
	}

	return m, tea.Batch(cmds...)
}

func (m *FrameModel) calcActualFrameWidth(termWidth int) int {
	frameWidth := m.config.MaxWidth()
	if frameWidth > (termWidth - m.config.MarginX()*2 - 1) {
		frameWidth = termWidth - m.config.MarginX()*2 - 1
	}

	frameWidthOverhead := m.config.PaddingX() * 2
	contentMaxWidth := terminal.MaxLineWidth(m.Content)

	if contentMaxWidth < (frameWidth - frameWidthOverhead) {
		frameWidth = contentMaxWidth + frameWidthOverhead
	}
	if frameWidth < 30 {
		frameWidth = 30
	}

	return frameWidth
}

// View renders the component
func (m *FrameModel) View() string {

	termWidth := terminal.GetWidth()
	if !m.frameWidthLock {
		m.frameWidth = m.calcActualFrameWidth(termWidth)
	}

	fsBuilder := NewFrameStyleBuilder(m.config).UseWidth(m.frameWidth)

	var content string

	if m.isConnecting || m.isThinking {
		content = m.spinnerManager.View()
		fsBuilder.UseLightBorderColor()

		fsBuilder.AddAdjuster(m.spinnerManager.AdjustContainerStyle)
		if m.isConnecting {
			fsBuilder.UseConnectingIcon()
		}
		if m.isThinking {
			fsBuilder.UseThinkingIcon()
		}
	} else {
		content = m.Content
	}

	if m.isContinuing {
		fsBuilder.OpenTop()
	}
	if m.toBeContinued {
		fsBuilder.OpenBottom()
	}

	frameContainerStyle := fsBuilder.Build()
	renderedContent := frameContainerStyle.Render(content)

	// For right-aligned frames, add padding to the left to push it to the right
	if m.config.Align() == lipgloss.Right {
		// Calculate padding needed (remaining 20% of terminal width)
		padding := strings.Repeat(" ", termWidth-m.frameWidth-2-m.config.MarginX()*2)

		// Add padding to each line
		lines := strings.Split(renderedContent, "\n")
		for i, line := range lines {
			lines[i] = padding + line
		}

		// Rejoin the lines
		renderedContent = strings.Join(lines, "\n")
	}

	return renderedContent
}

// StreamMessage creates a frame with vibrant pink border
// reading the content from an io.Reader in a streaming fashion
func StreamMessage(reader io.Reader, style FrameConfig) {
	var contentProcessor func(string) string

	if style.IsRichText() {
		r, _ := glamour.NewTermRenderer(
			glamour.WithAutoStyle(),
			glamour.WithPreservedNewLines(),
			glamour.WithEmoji(),
			glamour.WithWordWrap(80), // FIXME: set actual term width
		)
		contentProcessor = func(s string) string {
			out, _ := r.Render(s)
			return paging.TermTrimSuffix(out)
		}
	}

	writer := paging.NewChunkedLineWriter(
		paging.NewContentBuffer(contentProcessor),
		paging.NewChunker(6),
	)
	frame := NewFrame("", writer, style)
	streamFrames(frame, reader)
}

func ShowMessage(content string, style FrameConfig) {
	StreamMessage(strings.NewReader(content), style)
}

func StreamAia(reader io.Reader) {
	StreamMessage(reader, AiaStyle())
}

func streamFrames(frame *FrameModel, reader io.Reader) {
	var p *tea.Program
	bufReader := bufio.NewReader(reader)
	chunkWriter := frame.chunkedWriter
	chunksReader := frame.chunkedReader
	buf := make([]byte, 512)
	var programsStartWg sync.WaitGroup
	var programsDoneWg sync.WaitGroup
	var allDoneWg sync.WaitGroup

	start := func() {
		defer programsDoneWg.Done()
		defer allDoneWg.Done()
		p = tea.NewProgram(frame)
		programsStartWg.Done()
		if _, err := p.Run(); err != nil {
			panic(err)
		}
	}

	orchestrateStart := func() {
		// make sure the program is started before quitting:
		programsStartWg.Wait()

		if p != nil {
			p.Quit()
		}

		// wait for the program to exit before starting the next one
		programsDoneWg.Wait()

		programsStartWg.Add(1)
		programsDoneWg.Add(1)
		allDoneWg.Add(1)

		go start()
	}

	allDoneWg.Add(1)
	go func() {
		defer allDoneWg.Done()
		for {
			n, err := bufReader.Read(buf)
			if n > 0 {
				_, _ = chunkWriter.Write(buf[:n])
				programsStartWg.Wait()
				p.Send(contentMsg{})
			}

			for chunksReader.HasNextChunk() {
				frame.toBeContinued = true
				orchestrateStart()
				frame.Content = chunksReader.NextChunk()
				frame.toBeContinued = false
				frame.isContinuing = true
				frame.isConnecting = false
				frame.frameWidthLock = true
			}

			if err != nil {
				if err == io.EOF {
					programsStartWg.Wait()
					p.Quit()
					break
				}
				panic(err)
			}
		}
	}()

	orchestrateStart()

	allDoneWg.Wait()
	println("done")
}

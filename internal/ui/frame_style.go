package ui

import (
	"github.com/charmbracelet/lipgloss"
)

type FrameStyleBuilder struct {
	config         FrameConfig
	styleAdjusters []func(lipgloss.Style) lipgloss.Style
	useIcon        string
	useBorderColor lipgloss.Color
	useWidth       int
	openTop        bool
	openBottom     bool
}

func NewFrameStyleBuilder(config FrameConfig) *FrameStyleBuilder {
	return &FrameStyleBuilder{
		config:         config,
		useIcon:        config.icon,
		useBorderColor: config.borderColor,
		useWidth:       config.MaxWidth(),
	}
}

func (d *FrameStyleBuilder) UseConnectingIcon() *FrameStyleBuilder {
	d.useIcon = d.config.iconConnecting
	return d
}

func (d *FrameStyleBuilder) UseThinkingIcon() *FrameStyleBuilder {
	d.useIcon = d.config.iconThinking
	return d
}

func (d *FrameStyleBuilder) UseLightBorderColor() *FrameStyleBuilder {
	d.useBorderColor = d.config.BorderColorLight()
	return d
}

func (d *FrameStyleBuilder) UseWidth(width int) *FrameStyleBuilder {
	d.useWidth = width
	return d
}

func (d *FrameStyleBuilder) AddAdjuster(adj func(lipgloss.Style) lipgloss.Style) *FrameStyleBuilder {
	d.styleAdjusters = append(d.styleAdjusters, adj)
	return d
}

func (d *FrameStyleBuilder) OpenTop() *FrameStyleBuilder {
	d.openTop = true
	return d
}

func (d *FrameStyleBuilder) OpenBottom() *FrameStyleBuilder {
	d.openBottom = true
	return d
}

func (d *FrameStyleBuilder) Build() lipgloss.Style {
	style := lipgloss.NewStyle().
		Margin(d.adjustSpaces(d.config.MarginY(), d.config.MarginX())...).
		Border(d.buildBorderStyle(), d.buildBorderSides()...).
		BorderForeground(d.useBorderColor).
		Padding(d.adjustSpaces(d.config.PaddingY(), d.config.PaddingX())...).
		Align(d.config.Align()).
		Width(d.useWidth)

	for _, adj := range d.styleAdjusters {
		style = adj(style)
	}

	return style
}

func (d *FrameStyleBuilder) buildBorderStyle() lipgloss.Border {
	border := d.config.Border()

	useIcon := d.config.icon
	if d.useIcon != "" {
		useIcon = d.useIcon
	}

	useTitle := d.config.title

	useTopLeft := border.TopLeft
	if useIcon != "" || useTitle != "" {
		useTopLeft = border.TopLeft + border.Top
		if useIcon != "" {
			useTopLeft += " " + useIcon
		}
		if useTitle != "" {
			useTopLeft += " " + useTitle
		}
		useTopLeft += " "
	}

	border.TopLeft = useTopLeft

	if d.openTop {
		border.Top = ""
		border.TopLeft = ""
		border.TopRight = ""
	}
	if d.openBottom {
		border.Bottom = ""
		border.BottomLeft = ""
		border.BottomRight = ""
	}

	return border
}

func (d *FrameStyleBuilder) buildBorderSides() []bool {
	return []bool{
		!d.openTop,
		true,
		true,
		true,
	}
}

func (d *FrameStyleBuilder) adjustSpaces(y int, x int) []int {
	spaces := []int{
		y,
		x,
		y,
		x,
	}

	if d.openTop {
		spaces[0] = 0
	}
	if d.openBottom {
		spaces[2] = 0
	}

	return spaces
}

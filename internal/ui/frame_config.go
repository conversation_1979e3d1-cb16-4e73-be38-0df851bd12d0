package ui

import (
	"github.com/charmbracelet/lipgloss"
	"github.com/oxio/aia/internal/colors"
)

type FrameConfig struct {
	marginX          int
	marginY          int
	paddingX         int
	paddingY         int
	borderColor      lipgloss.Color
	borderColorLight lipgloss.Color
	border           lipgloss.Border
	align            lipgloss.Position
	maxWidth         int
	icon             string
	iconConnecting   string
	iconThinking     string
	title            string
	isRichText       bool
}

func NewFrameConfig() FrameConfig {
	return FrameConfig{
		marginX:          1,
		marginY:          1,
		paddingX:         2,
		paddingY:         1,
		borderColor:      colors.VibrantPink,
		borderColorLight: colors.DimmedPink5,
		border:           lipgloss.RoundedBorder(),
		align:            lipgloss.Left,
		iconConnecting:   "🔌",
		iconThinking:     "🤔",
		maxWidth:         120,
		isRichText:       false,
	}
}

// MarginX returns the horizontal margin
func (fc FrameConfig) MarginX() int {
	return fc.marginX
}

// WithMarginX sets the horizontal margin and returns a new config instance
func (fc FrameConfig) WithMarginX(margin int) FrameConfig {
	newFc := fc
	newFc.marginX = margin
	return newFc
}

// MarginY returns the vertical margin
func (fc FrameConfig) MarginY() int {
	return fc.marginY
}

// WithMarginY sets the vertical margin and returns a new config instance
func (fc FrameConfig) WithMarginY(margin int) FrameConfig {
	newFc := fc
	newFc.marginY = margin
	return newFc
}

// PaddingX returns the horizontal padding
func (fc FrameConfig) PaddingX() int {
	if fc.isRichText {
		if fc.paddingX >= 2 {
			return fc.paddingX - 2
		}
		return 0
	}
	return fc.paddingX
}

func (fc FrameConfig) PaddingXRaw() int {
	return fc.paddingX
}

// WithPaddingX sets the horizontal padding and returns a new config instance
func (fc FrameConfig) WithPaddingX(padding int) FrameConfig {
	newFc := fc
	newFc.paddingX = padding
	return newFc
}

// PaddingY returns the vertical padding
func (fc FrameConfig) PaddingY() int {
	return fc.paddingY
}

func (fc FrameConfig) PaddingYRaw() int {
	return fc.paddingY
}

// WithPaddingY sets the vertical padding and returns a new config instance
func (fc FrameConfig) WithPaddingY(padding int) FrameConfig {
	newFc := fc
	newFc.paddingY = padding
	return newFc
}

// BorderColor returns the border color
func (fc FrameConfig) BorderColor() lipgloss.Color {
	return fc.borderColor
}

// WithBorderColor sets the border color and returns a new config instance
func (fc FrameConfig) WithBorderColor(color lipgloss.Color) FrameConfig {
	newFc := fc
	newFc.borderColor = color
	return newFc
}

func (fc FrameConfig) BorderColorLight() lipgloss.Color {
	return fc.borderColorLight
}

func (fc FrameConfig) WithBorderColorLight(color lipgloss.Color) FrameConfig {
	newFc := fc
	newFc.borderColorLight = color
	return newFc
}

// Border returns the border config with title and icon if present
func (fc FrameConfig) Border() lipgloss.Border {
	return lipgloss.Border{
		Top:         fc.border.Top,
		Bottom:      fc.border.Bottom,
		Left:        fc.border.Left,
		Right:       fc.border.Right,
		TopLeft:     fc.border.TopLeft,
		TopRight:    fc.border.TopRight,
		BottomLeft:  fc.border.BottomLeft,
		BottomRight: fc.border.BottomRight,
	}
}

// WithBorder sets the border config and returns a new config instance
func (fc FrameConfig) WithBorder(border lipgloss.Border) FrameConfig {
	newFc := fc
	newFc.border = border
	return newFc
}

// Align returns the horizontal alignment
func (fc FrameConfig) Align() lipgloss.Position {
	return fc.align
}

// WithAlign sets the horizontal alignment and returns a new config instance
func (fc FrameConfig) WithAlign(align lipgloss.Position) FrameConfig {
	newFc := fc
	newFc.align = align
	return newFc
}

func (fc FrameConfig) WithMaxWidth(width int) FrameConfig {
	newFc := fc
	newFc.maxWidth = width
	return newFc
}

func (fc FrameConfig) MaxWidth() int {
	if fc.isRichText {
		return 80
	}
	return fc.maxWidth
}

// Icon returns the icon
func (fc FrameConfig) Icon() string {
	return fc.icon
}

// WithIcon sets the icon and returns a new config instance
func (fc FrameConfig) WithIcon(icon string) FrameConfig {
	newFc := fc
	newFc.icon = icon
	return newFc
}

// Title returns the title
func (fc FrameConfig) Title() string {
	return fc.title
}

// WithTitle sets the title and returns a new config instance
func (fc FrameConfig) WithTitle(title string) FrameConfig {
	newFc := fc
	newFc.title = title
	return newFc
}

func (fc FrameConfig) WithRichText() FrameConfig {
	if fc.isRichText {
		return fc
	}
	newFc := fc
	newFc.isRichText = true
	return newFc
}

func (fc FrameConfig) WithoutRichText() FrameConfig {
	if !fc.isRichText {
		return fc
	}
	newFc := fc
	newFc.isRichText = false
	return newFc
}

func (fc FrameConfig) IsRichText() bool {
	return fc.isRichText
}

func AiaStyle() FrameConfig {
	return NewFrameConfig().
		WithBorderColor(colors.VibrantPink).
		//WithIcon("\u2728\u200B"). // sparkles
		//WithIcon("\U0001F31F").   // glowing star
		WithIcon("\U0001F4AB"). // dizzy
		WithTitle("AIA")
}

func UserStyle() FrameConfig {
	return NewFrameConfig().
		WithBorderColor(colors.DeepSkyBlue).
		WithIcon("👤").
		WithTitle("You").
		WithAlign(lipgloss.Right)
}

func ErrorStyle() FrameConfig {
	return NewFrameConfig().
		WithBorderColor(colors.Red).
		WithIcon("❌").
		WithTitle("Error")
}

func WarningStyle() FrameConfig {
	return NewFrameConfig().
		WithBorderColor(colors.PaleGold).
		WithIcon("⚡").
		WithTitle("Warning")
}

func InfoStyle() FrameConfig {
	return NewFrameConfig().
		WithBorderColor(colors.SteelBlue).
		WithIcon("💡").
		WithTitle("Info")
}

func SuccessStyle() FrameConfig {
	return NewFrameConfig().
		WithBorderColor(colors.MintLeaf).
		WithIcon("✅").
		WithTitle("Success")
}

// WithMargin sets both horizontal and vertical margins and returns a new config instance
func (fc FrameConfig) WithMargin(vertical, horizontal int) FrameConfig {
	newFc := fc
	newFc.marginX = horizontal
	newFc.marginY = vertical
	return newFc
}

// WithPadding sets both horizontal and vertical paddings and returns a new config instance
func (fc FrameConfig) WithPadding(vertical, horizontal int) FrameConfig {
	newFc := fc
	newFc.paddingX = horizontal
	newFc.paddingY = vertical
	return newFc
}

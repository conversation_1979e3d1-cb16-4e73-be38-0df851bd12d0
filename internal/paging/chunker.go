package paging

import (
	"strings"
)

type Chunker struct {
	linesPerChunk int
}

func NewChunker(linesPerChunk int) *Chunker {
	if linesPerChunk <= 0 {
		panic("linesPerChunk must be greater than 0")
	}
	return &Chunker{
		linesPerChunk: linesPerChunk,
	}
}

func (c *Chunker) Chunk(content string) []string {
	chunks := []string{""}

	lines := splitPreserveNewline(content)
	for _, line := range lines {
		// Add line to the last chunk
		chunks[len(chunks)-1] += line

		// Check if the last chunk has reached the line limit
		if visibleLineCountInLastChunk(chunks) >= c.linesPerChunk {
			chunks[len(chunks)-1] = TermTrimSuffix(chunks[len(chunks)-1])
			// Only create a new chunk if there are more lines to process
			// or if this isn't the last line in the current batch
			chunks = append(chunks, "")
		}
	}

	return chunks
}

// visibleLineCountInLastChunk counts actual displayed lines in the last chunk
func visibleLineCountInLastChunk(chunks []string) int {
	if len(chunks) == 0 {
		return 0
	}
	lastChunk := chunks[len(chunks)-1]
	return strings.Count(lastChunk, "\n")
}

// splitPreserveNewline splits input into lines keeping the newline char.
func splitPreserveNewline(s string) []string {
	var lines []string
	var buf strings.Builder

	for i := 0; i < len(s); i++ {
		ch := s[i]
		buf.WriteByte(ch)

		if ch == '\n' {
			lines = append(lines, buf.String())
			buf.Reset()
		}
	}
	if buf.Len() > 0 {
		lines = append(lines, buf.String())
	}
	return lines
}

package paging

import (
	"strings"
	"unicode"
)

const resetSeq = "\x1b[0m"

func TermTrimSuffix(s string) string {
	return s
	
	for {
		if len(s) == 0 {
			break
		}
		if strings.HasSuffix(s, resetSeq) {
			s = strings.TrimSuffix(s, resetSeq)
			continue
		}
		r := rune(s[len(s)-1])
		if unicode.IsSpace(r) {
			s = strings.TrimRightFunc(s, unicode.IsSpace)
			continue
		}

		break
	}

	return s
}

func TermTrimPrefix(s string) string {
	for {
		if len(s) == 0 {
			break
		}
		if strings.HasPrefix(s, resetSeq) {
			s = strings.TrimPrefix(s, resetSeq)
			continue
		}
		r := rune(s[0])
		if unicode.IsSpace(r) {
			s = strings.TrimLeftFunc(s, unicode.IsSpace)
			continue
		}

		break
	}

	return s
}

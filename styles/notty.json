{"document": {"block_prefix": "\n", "block_suffix": "\n", "margin": 2}, "block_quote": {"indent": 1, "indent_token": "| "}, "paragraph": {}, "list": {"level_indent": 4}, "heading": {"block_suffix": "\n"}, "h1": {"prefix": "# "}, "h2": {"prefix": "## "}, "h3": {"prefix": "### "}, "h4": {"prefix": "#### "}, "h5": {"prefix": "##### "}, "h6": {"prefix": "###### "}, "text": {}, "strikethrough": {"block_prefix": "~~", "block_suffix": "~~"}, "emph": {"block_prefix": "*", "block_suffix": "*"}, "strong": {"block_prefix": "**", "block_suffix": "**"}, "hr": {"format": "\n--------\n"}, "item": {"block_prefix": "• "}, "enumeration": {"block_prefix": ". "}, "task": {"ticked": "[x] ", "unticked": "[ ] "}, "link": {}, "link_text": {}, "image": {}, "image_text": {"format": "Image: {{.text}} →"}, "code": {"block_prefix": "`", "block_suffix": "`"}, "code_block": {"margin": 2}, "table": {}, "definition_list": {}, "definition_term": {}, "definition_description": {"block_prefix": "\n* "}, "html_block": {}, "html_span": {}}
{"document": {"margin": 2}, "block_quote": {"indent": 1, "indent_token": "│ "}, "paragraph": {}, "list": {"level_indent": 2}, "heading": {"block_suffix": "\n", "color": "212", "bold": true}, "h1": {"block_prefix": "\n", "block_suffix": "\n"}, "h2": {"prefix": "▌ "}, "h3": {"prefix": "┃ "}, "h4": {"prefix": "│ "}, "h5": {"prefix": "┆ "}, "h6": {"prefix": "┊ ", "bold": false}, "text": {}, "strikethrough": {"crossed_out": true}, "emph": {"italic": true}, "strong": {"bold": true}, "hr": {"color": "212", "format": "\n──────\n"}, "item": {"block_prefix": "• "}, "enumeration": {"block_prefix": ". "}, "task": {"ticked": "[✓] ", "unticked": "[ ] "}, "link": {"color": "99", "underline": true}, "link_text": {"bold": true}, "image": {"underline": true}, "image_text": {"format": "Image: {{.text}}"}, "code": {"prefix": " ", "suffix": " ", "color": "212", "background_color": "236"}, "code_block": {}, "table": {}, "definition_list": {}, "definition_term": {}, "definition_description": {"block_prefix": "\n🠶 "}, "html_block": {}, "html_span": {}}
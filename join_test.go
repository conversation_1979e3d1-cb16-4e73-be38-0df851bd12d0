package lipgloss

import "testing"

func TestJoinVertical(t *testing.T) {
	type test struct {
		name     string
		result   string
		expected string
	}
	tests := []test{
		{"pos0", <PERSON><PERSON><PERSON><PERSON><PERSON>(Left, "A", "BBBB"), "A   \nBBBB"},
		{"pos1", <PERSON><PERSON><PERSON><PERSON><PERSON>(Right, "A", "BBBB"), "   A\nBBBB"},
		{"pos0.25", <PERSON><PERSON>V<PERSON><PERSON>(0.25, "A", "BBBB"), " A  \nBBBB"},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			if test.result != test.expected {
				t.<PERSON>rrorf("Got \n%s\n, expected \n%s\n", test.result, test.expected)
			}
		})
	}
}

func TestJoinHorizontal(t *testing.T) {
	type test struct {
		name     string
		result   string
		expected string
	}
	tests := []test{
		{"pos0", Jo<PERSON><PERSON><PERSON>zontal(Top, "A", "B\nB\nB\nB"), "AB\n B\n B\n B"},
		{"pos1", <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(Bottom, "A", "B\nB\nB\nB"), " B\n B\n B\nAB"},
		{"pos0.25", <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(0.25, "A", "B\nB\nB\nB"), " B\nAB\n B\n B"},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			if test.result != test.expected {
				t.Errorf("Got \n%s\n, expected \n%s\n", test.result, test.expected)
			}
		})
	}
}

= aia - The AI Assistant

*AI first* - AIA is a completely new way to interact with the computer.

_Forget browsing through man pages. AIA is the one command that you need._

AIA revolutionizes your command line workflow, but that's just the start!
Unleash AIA's core intelligence by leveraging it as a powerful backend service.
Seamlessly integrate its capabilities to supercharge your own applications.

Example applications where AIA's backend capabilities can be leveraged:

✅ Websites ✅ Chat bots ✅ GUI applications ✅ Automation tools ✅ A2A networks ✅ MCP servers

AIA runs in all major shells like `bash`, `zsh`, `fish`, `powershell` etc.


== Features

- Minimalistic
- Compatible with Ollama and OpanAI APIs
- Zero dependencies (no need for additional runtime like Node, Python, etc.)
- Optional Meta model for enhanced User Experience
- Platform independence
- Single binary

== What is Meta model?

Meta model is used to enrich the user experience when issuing prompts.

Example use cases:

- Determine if the prompt contains references to filesystem and attaching its contents to the prompt

- Determine if the output command from LLM is safe or dangerous to execute

Meta model is configured using advanced configuration.
You should usually use faster LLM for Meta model then th Main model.

You can also use local Ollama model for extra security.

Meta model should usually be faster than Main model.
In particular the TTFT (Time To First Token) is important.
